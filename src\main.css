:root {
  /* 网站主要颜色 */
  --base-color: #ebba80;
  --secondary-color: #dd9d94;
  /* base次要颜色 */
  --base-light: hsl(from var(--base-color) h s calc(l + 10));
  /* base强调色 */
  --base-accent: hsl(from var(--base-color) h s calc(l - 10));
  --base-accent-2: hsl(from var(--base-color) h s calc(l - 20));
  /* secondary次要颜色 */
  --secondary-light: hsl(from var(--secondary-color) h s calc(l + 10));
  /* secondary强调色 */
  --secondary-accent: hsl(from var(--secondary-color) h s calc(l - 10));
}

main {
  width: 100%;
}

.pill {
  display: inline-block;
  background: var(--secondary-accent);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 1.1rem;
  font-weight: 500;
  color: white;
  cursor: pointer;
  margin: 0.5rem;
  margin-top: 1.5rem;
}

/* 去除a标签基本样式 */
a {
  text-decoration: none;
  color: inherit;
}
/* grid布局 */
.layout {
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 2rem;
}
