<template>
  <div id="PostDetails">
    <template v-if="error">
      {{ error }}
    </template>
    <template v-if="post">
      <!-- 文章标题 -->
      <h1 class="post-title">{{ post.title }}</h1>
      <!-- 文章内容 -->
      <div class="post-content">
        <p>{{ post.content }}</p>
      </div>
    </template>
    <template v-else>
      <p>加载中...</p>
    </template>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import getPost from '@/composables/getPost';
const route = useRoute();
const postID = route.params.id;

const { post, error, load } = getPost();

load(postID);
</script>

<style scoped></style>
