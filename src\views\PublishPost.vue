<template>
  <div id="PublishPost">
    <form @submit.prevent="handleSubmit">
      <label>标题：</label>
      <input v-model="title" type="text" required />

      <label>文章正文：</label>
      <textarea v-model="body" required></textarea>

      <label>Tags(按回车添加tag)</label>
      <input type="text" v-model="tags">

      <button>发布</button>
    </form>
  </div>
</template>

<script setup>

</script>

<style scoped>
/* 主容器样式 */
#PublishPost {
  width: 100%;
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
  min-height: 70vh;
}

/* 表单容器样式 */
form {
  background: #fefefe;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--base-light);
  position: relative;
  overflow: hidden;
}

/* 表单顶部装饰条 */
form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--base-color), var(--secondary-color), var(--base-color));
}

/* 标签样式 */
label {
  display: block;
  font-size: 1.1em;
  font-weight: 600;
  color: var(--base-accent-2);
  margin: 1.5rem 0 0.5rem 0;
  position: relative;
}

/* 第一个标签的上边距 */
label:first-of-type {
  margin-top: 0;
}

/* 输入框通用样式 */
input[type="text"],
textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--base-light);
  border-radius: 8px;
  font-size: 1em;
  color: #333;
  background-color: #fff;
  transition: all 0.3s ease;
  font-family: inherit;
  box-sizing: border-box;
}

/* 输入框聚焦状态 */
input[type="text"]:focus,
textarea:focus {
  outline: none;
  border-color: var(--base-color);
  box-shadow: 0 0 0 3px rgba(235, 186, 128, 0.2);
  transform: translateY(-1px);
}

/* 输入框悬停状态 */
input[type="text"]:hover,
textarea:hover {
  border-color: var(--base-accent);
}

/* 标题输入框特殊样式 */
input[type="text"] {
  font-size: 1.1em;
  font-weight: 500;
}

/* 文本域样式 */
textarea {
  min-height: 200px;
  resize: vertical;
  line-height: 1.6;
  font-size: 1em;
}

/* 标签输入框样式 */
input[type="text"]:last-of-type {
  background-color: var(--base-light);
  border-color: var(--base-accent);
}

input[type="text"]:last-of-type:focus {
  background-color: #fff;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(221, 157, 148, 0.2);
}

/* 表单悬停效果 */
form:hover {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  #PublishPost {
    margin: 1rem auto;
    padding: 0 0.5rem;
  }

  form {
    padding: 2rem 1.5rem;
  }

  label {
    font-size: 1em;
    margin: 1.2rem 0 0.4rem 0;
  }

  input[type="text"],
  textarea {
    padding: 0.8rem;
    font-size: 0.95em;
  }

  textarea {
    min-height: 150px;
  }
}

@media (max-width: 480px) {
  form {
    padding: 1.5rem 1rem;
  }

  label {
    font-size: 0.95em;
    margin: 1rem 0 0.3rem 0;
  }

  input[type="text"],
  textarea {
    padding: 0.7rem;
    font-size: 0.9em;
  }

  textarea {
    min-height: 120px;
  }
}

/* 平滑过渡 */
form,
input[type="text"],
textarea {
  transition: all 0.3s ease;
}

/* 必填字段标识
input[required]+label::after,
textarea[required]+label::after {
  content: ' *';
  color: var(--secondary-accent);
  font-weight: bold;
} */

/* 输入验证状态 */
input:invalid,
textarea:invalid {
  border-color: var(--secondary-accent);
}

input:valid,
textarea:valid {
  border-color: var(--base-color);
}
</style>
